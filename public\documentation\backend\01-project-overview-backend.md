# Bean Software Backend - Project Overview

## Table of Contents
- [Introduction](#introduction)
- [Architecture Overview](#architecture-overview)
- [Technology Stack](#technology-stack)
- [Project Structure](#project-structure)
- [Multi-Language Support](#multi-language-support)
- [Database Architecture](#database-architecture)
- [Applications Overview](#applications-overview)
- [Key Features](#key-features)
- [Development Setup](#development-setup)
- [Configuration](#configuration)

## Introduction

Bean Software Backend is a Django-based web application designed to serve as the backend infrastructure for Bean Software's digital presence. The application is built with a focus on internationalization, specifically supporting English and Turkish languages with separate database storage to comply with Turkish data localization laws.

## Architecture Overview

The backend follows Django's Model-View-Template (MVT) architecture pattern with several custom enhancements:

- **Multi-Database Architecture**: Separate databases for English and Turkish content
- **Custom Database Routing**: Intelligent routing based on language context
- **Middleware-Based Language Detection**: Automatic language detection and context setting
- **Modular App Structure**: Organized into focused Django applications

## Technology Stack

- **Framework**: Django 5.2
- **Database**: SQLite (dual databases: `db.sqlite3` and `db_turkish.sqlite3`)
- **Language**: Python 3.x
- **Internationalization**: Django's built-in i18n framework
- **Development Server**: Django development server (port 8001)

## Project Structure

```
Bean-Software-Backend/
├── bean_software_backend/          # Main Django project
│   ├── __init__.py
│   ├── settings.py                 # Project configuration
│   ├── urls.py                     # Main URL routing
│   ├── wsgi.py                     # WSGI configuration
│   └── asgi.py                     # ASGI configuration
├── core/                           # Core functionality app
│   ├── middleware.py               # Language detection middleware
│   ├── routers.py                  # Database routing logic
│   ├── models.py                   # Base models
│   ├── views.py                    # Core views
│   ├── urls.py                     # Core URL patterns
│   └── management/                 # Custom management commands
│       └── commands/
│           ├── migrate_turkish.py
│           └── create_turkish_tables.py
├── content/                        # Content management app
├── team/                           # Team information app
├── technologies/                   # Technologies showcase app
├── demos/                          # Demo projects app
├── api/                           # API endpoints
├── locale/                        # Translation files
│   ├── en/                        # English translations
│   └── tr/                        # Turkish translations
├── manage.py                      # Django management script
├── db.sqlite3                     # English database
├── db_turkish.sqlite3             # Turkish database
└── database_schema.dbml           # Database schema documentation
```

## Multi-Language Support

### Supported Languages
- **English (en)**: Default language, stored in primary database
- **Turkish (tr)**: Secondary language, stored in separate Turkish database

### Language Detection Priority
1. URL parameter (`?lang=tr`)
2. Language cookie (`bean_language`)
3. Accept-Language header
4. Default language from settings (`en`)

### Key Configuration
```python
LANGUAGES = [
    ('en', 'English'),
    ('tr', 'Türkçe'),
]

LANGUAGE_DATABASE_MAPPING = {
    'en': 'default',
    'tr': 'turkish',
}
```

## Database Architecture

### Dual Database Setup
The application uses two separate SQLite databases to comply with Turkish data localization requirements:

- **Default Database** (`db.sqlite3`): Stores English content and Django system data
- **Turkish Database** (`db_turkish.sqlite3`): Stores Turkish content exclusively

### Database Routing
Custom `LanguageRouter` class handles automatic routing:
- Turkish models (suffixed with 'TR') → Turkish database
- All other models → Default database
- Django built-in apps → Default database only

### Model Architecture
- **Base Models**: Standard Django models for English content
- **Turkish Models**: Inherit from `TurkishBaseModel` for automatic Turkish database routing
- **Abstract Base Classes**: Provide consistent structure across language variants

## Applications Overview

### Core App
- **Purpose**: Central functionality and infrastructure
- **Key Components**:
  - Language detection middleware
  - Database routing logic
  - Base model classes
  - Management commands for Turkish database setup

### Content App
- **Purpose**: Content management system
- **Features**: Dual-language content storage with separate models

### Team App
- **Purpose**: Team member information management
- **Features**: Team profiles with multi-language support

### Technologies App
- **Purpose**: Technology stack showcase
- **Features**: Technology listings with descriptions in both languages

### Demos App
- **Purpose**: Project demonstrations and portfolio
- **Features**: Demo project showcases with multi-language descriptions

### API App
- **Purpose**: RESTful API endpoints
- **Features**: API endpoints for frontend consumption

## Key Features

### 1. Intelligent Language Detection
- Automatic language detection from multiple sources
- Persistent language preferences via cookies
- URL-based language switching

### 2. Compliant Data Storage
- Turkish data stored exclusively in Turkish database
- Meets Turkish data localization requirements
- Separate migration and management for Turkish content

### 3. Custom Middleware Stack
- `LanguageDetectionMiddleware`: Detects and sets user language
- `DatabaseLanguageMiddleware`: Ensures proper database context
- `LanguageRedirectMiddleware`: Handles language-based redirects

### 4. Management Commands
- `migrate_turkish`: Creates and migrates Turkish database tables
- `create_turkish_tables`: Manual Turkish table creation

### 5. Flexible Model Architecture
- Abstract base classes for consistent structure
- Automatic database routing based on model type
- Support for both English and Turkish content variants

## Development Setup

### Prerequisites
- Python 3.x
- Virtual environment (`.venv/` already configured)
- Django (already installed)

### Quick Start
```bash
# Navigate to project directory
cd Bean-Software-Backend

# Activate virtual environment
.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac

# Run migrations
python manage.py migrate
python manage.py migrate --database=turkish

# Create Turkish tables
python manage.py create_turkish_tables

# Start development server
python manage.py runserver 8001
```

### Access Points
- **Main Application**: http://127.0.0.1:8001/
- **Admin Interface**: http://127.0.0.1:8001/admin/
- **Language Switching**: Add `?lang=tr` or `?lang=en` to any URL

## Configuration

### Key Settings
- **DEBUG**: Currently set to `True` for development
- **ALLOWED_HOSTS**: Empty list for development (needs configuration for production)
- **LANGUAGE_COOKIE_AGE**: 1 year (365 days)
- **DEFAULT_CONTENT_LANGUAGE**: English (`en`)

### Database Configuration
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    },
    'turkish': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db_turkish.sqlite3',
    }
}
```

### Middleware Configuration
```python
MIDDLEWARE = [
    'core.middleware.LanguageDetectionMiddleware',
    'core.middleware.DatabaseLanguageMiddleware',
    'core.middleware.LanguageRedirectMiddleware',
    # ... other middleware
]
```

This backend architecture provides a robust foundation for a multi-language web application with compliant data storage and intelligent language handling capabilities.
