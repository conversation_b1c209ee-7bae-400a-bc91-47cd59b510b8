import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface MarkdownRendererProps {
  filePath: string;
  className?: string;
}

export function MarkdownRenderer({ filePath, className }: MarkdownRendererProps) {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadMarkdown = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // In a real application, you would fetch this from your backend
        // For now, we'll simulate loading the markdown content
        const response = await fetch(`/documentation/${filePath}`);
        
        if (!response.ok) {
          throw new Error(`Failed to load documentation: ${response.statusText}`);
        }
        
        const text = await response.text();
        setContent(text);
      } catch (err) {
        console.error('Error loading markdown:', err);
        setError(err instanceof Error ? err.message : 'Failed to load documentation');
        
        // For development, provide fallback content
        if (filePath.includes('frontend')) {
          setContent(getFrontendFallbackContent());
        } else if (filePath.includes('backend')) {
          setContent(getBackendFallbackContent());
        }
      } finally {
        setLoading(false);
      }
    };

    loadMarkdown();
  }, [filePath]);

  if (loading) {
    return (
      <div className={className}>
        <div className="space-y-4">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
          <div className="space-y-2">
            <Skeleton className="h-6 w-1/2" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </div>
      </div>
    );
  }

  if (error && !content) {
    return (
      <div className={className}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className={className}>
      <Card>
        <CardContent className="p-6">
          <div 
            className="prose prose-slate max-w-none dark:prose-invert prose-headings:text-foreground prose-p:text-muted-foreground prose-strong:text-foreground prose-code:text-foreground prose-pre:bg-muted prose-pre:text-foreground"
            dangerouslySetInnerHTML={{ __html: formatMarkdownToHtml(content) }}
          />
        </CardContent>
      </Card>
    </div>
  );
}

// Simple markdown to HTML converter for basic formatting
function formatMarkdownToHtml(markdown: string): string {
  return markdown
    // Headers
    .replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold mt-6 mb-3">$1</h3>')
    .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold mt-8 mb-4">$1</h2>')
    .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-8 mb-6">$1</h1>')
    // Bold and italic
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // Code blocks
    .replace(/```([\s\S]*?)```/g, '<pre class="bg-muted p-4 rounded-lg overflow-x-auto"><code>$1</code></pre>')
    // Inline code
    .replace(/`([^`]+)`/g, '<code class="bg-muted px-1 py-0.5 rounded text-sm">$1</code>')
    // Lists
    .replace(/^\- (.*$)/gim, '<li class="ml-4">• $1</li>')
    // Paragraphs
    .replace(/\n\n/g, '</p><p class="mb-4">')
    // Wrap in paragraph tags
    .replace(/^/, '<p class="mb-4">')
    .replace(/$/, '</p>');
}

function getFrontendFallbackContent(): string {
  return `# Bean Software Frontend - Project Overview

## Introduction

Bean Software Frontend is a modern React-based admin panel built with TypeScript and Vite. The application serves as the administrative interface for Bean Software's digital presence.

## Technology Stack

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Icons**: Lucide React
- **Routing**: React Router DOM

## Key Features

### Modern Admin Interface
- Clean, responsive design
- Dark/light theme support
- Intuitive navigation and user experience

### Content Management System
- Multi-language content support
- Rich text editing capabilities
- Media management

### Development Tools
- Built-in documentation system
- Component library
- Development utilities

## Development Setup

\`\`\`bash
# Install dependencies
npm install

# Start development server
npm run dev
\`\`\`

Access the application at **http://localhost:5173/**`;
}

function getBackendFallbackContent(): string {
  return `# Bean Software Backend - Project Overview

## Introduction

Bean Software Backend is a Django-based web application designed to serve as the backend infrastructure for Bean Software's digital presence.

## Technology Stack

- **Framework**: Django 5.2
- **Database**: SQLite (dual databases)
- **Language**: Python 3.x
- **Internationalization**: Django's built-in i18n framework

## Architecture Overview

The backend follows Django's Model-View-Template (MVT) architecture pattern with several custom enhancements:

- **Multi-Database Architecture**: Separate databases for English and Turkish content
- **Custom Database Routing**: Intelligent routing based on language context
- **Middleware-Based Language Detection**: Automatic language detection and context setting
- **Modular App Structure**: Organized into focused Django applications

## Key Features

### Intelligent Language Detection
- Automatic language detection from multiple sources
- Persistent language preferences via cookies
- URL-based language switching

### Compliant Data Storage
- Turkish data stored exclusively in Turkish database
- Meets Turkish data localization requirements

## Development Setup

\`\`\`bash
# Activate virtual environment
.venv\\Scripts\\activate

# Run migrations
python manage.py migrate
python manage.py migrate --database=turkish

# Start development server
python manage.py runserver 8001
\`\`\`

Access the application at **http://127.0.0.1:8001/**`;
}
